# App Environment
NODE_ENV=development
API_DOC_VERSION=1.0.0
PORT=7009
HOST=localhost
API_PREFIX=api
APP_NAME=NestJS Project Template
APP_DESCRIPTION=NestJS template with best practices
API_VERSION=1.0
APP_URL=http://localhost:7009

# MongoDB Configuration
DATABASE_URL=mongodb://localhost:27017/nestjs_practice?replicaSet=rs0

# Auth
ALLOWED_ORIGINS=localhost:*,127.0.0.1,localhost:7009
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=30d
JWT_REFRESH_SECRET=your-refresh-secret-key
JWT_REFRESH_EXPIRES_IN=90d

# Mail Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_SECURE=false
MAIL_USER=<EMAIL>
MAIL_PASSWORD=your-app-specific-password
MAIL_FROM=<EMAIL>

# Storage
STORAGE_PROVIDER=s3
STORAGE_BUCKET=my-bucket
STORAGE_REGION=us-east-1
STORAGE_ENDPOINT=s3.us-east-1.amazonaws.com
STORAGE_ACCESS_KEY_ID=your-access-key-id
STORAGE_ACCESS_KEY_SECRET=your-access-key-secret
STORAGE_BASE_URL=https://my-bucket.s3.us-east-1.amazonaws.com
STORAGE_MAX_FILE_SIZE=10485760
STORAGE_ALLOWED_MIME_TYPES=image/jpeg,image/png,image/gif,application/pdf




# Storage - Tencent COS (uncomment to use)
# STORAGE_PROVIDER=tencentoss
# STORAGE_BUCKET=my-bucket
# STORAGE_REGION=ap-guangzhou
# STORAGE_ACCESS_KEY_ID=your-secret-id
# STORAGE_ACCESS_KEY_SECRET=your-secret-key
# STORAGE_BASE_URL=https://my-bucket.cos.ap-guangzhou.myqcloud.com
# STORAGE_MAX_FILE_SIZE=10485760
# STORAGE_ALLOWED_MIME_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Datadog Configuration
DATADOG_API_KEY=your-datadog-api-key
DATADOG_SERVICE_NAME=nestjs-structure-practices
DATADOG_HOST_NAME=local-dev
DATADOG_INTAKE_REGION=us5

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_test_secret_key
STRIPE_PUBLIC_KEY=pk_test_your_test_public_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_API_VERSION=2025-02-24.acacia

# OAuth Configuration
OAUTH_PROVIDERS=github,apple
OAUTH_CALLBACK_URL_BASE=http://localhost:7009/api/v1/auth/callback
OAUTH_COOKIE_NAME=auth_session
OAUTH_COOKIE_MAX_AGE=2592000
OAUTH_COOKIE_SECURE=false

# OAuth Providers - GitHub
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# OAuth Providers - Apple (example)
APPLE_CLIENT_ID=your_apple_client_id
APPLE_CLIENT_SECRET=your_apple_client_secret